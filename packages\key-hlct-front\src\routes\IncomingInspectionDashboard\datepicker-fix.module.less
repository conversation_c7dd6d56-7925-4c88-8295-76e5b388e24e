/*
 * DatePicker样式修复文件 - 优化版本
 * 修复时间选择器下拉样式问题
 */

// 定义CSS变量以提高可维护性
:root {
  --datepicker-bg: #0c1a3e;
  --datepicker-border: #1a3a8b;
  --datepicker-text: #ffffff;
  --datepicker-hover: #4a90e2;
  --datepicker-selected: #4f8efe;
  --datepicker-today: rgba(74, 144, 226, 0.5);
  --datepicker-shadow: rgba(0, 0, 0, 0.5);
}

// 核心弹窗样式 - 简化选择器
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup) {
  background-color: var(--datepicker-bg);
  border: 1px solid var(--datepicker-border);
  border-radius: 4px;
  padding: 1rem;
  box-shadow: 0 4px 12px var(--datepicker-shadow);
  min-width: 360px;
  width: 360px;
  min-height: 420px;
  z-index: 9999;
  color: var(--datepicker-text);

  // 日历头部
  .c7n-pro-calendar-header {
    display: flex;
    color: var(--datepicker-text);
    border-bottom: 1px solid var(--datepicker-border);
    background: transparent;
    padding: 12px 16px;
    align-items: center;
    justify-content: space-between;
    min-height: 48px;

    // 年月选择器
    .c7n-pro-calendar-year-select,
    .c7n-pro-calendar-month-select {
      color: var(--datepicker-text);
      background: rgba(26, 58, 139, 0.3);
      border: 1px solid rgba(185, 212, 255, 0.3);
      border-radius: 4px;
      padding: 6px 12px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(26, 58, 139, 0.5);
        border-color: var(--datepicker-hover);
      }
    }

    // 导航按钮
    button {
      display: flex;
      color: #b9d4ff;
      background: rgba(26, 58, 139, 0.3);
      border: 1px solid rgba(185, 212, 255, 0.3);
      border-radius: 4px;
      padding: 8px 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 16px;
      min-width: 36px;
      height: 36px;
      align-items: center;
      justify-content: center;

      &:hover {
        color: var(--datepicker-text);
        background-color: var(--datepicker-border);
        border-color: var(--datepicker-hover);
        transform: scale(1.05);
      }
    }
  }

  // 日历主体
  .c7n-pro-calendar-body {
    background: transparent;
    width: 100%;
    min-height: 240px;
    padding: 8px;

    table {
      width: 100%;
      table-layout: fixed;
      border-collapse: separate;
      border-spacing: 2px;
      min-height: 200px;

      thead th {
        color: #b9d4ff;
        background: transparent;
        border: none;
        padding: 12px 8px;
        text-align: center;
        font-weight: bold;
        font-size: 14px;
        width: 14.28%;
        height: 40px;
      }

      // 日期单元格
      td.c7n-pro-calendar-cell {
        border: none;
        padding: 2px;
        text-align: center;
        width: 14.28%;
        height: 34px;
        vertical-align: middle;
        position: relative;
        box-sizing: border-box;

        .c7n-pro-calendar-cell-inner {
          width: 100%;
          height: 100%;
          color: var(--datepicker-text);
          font-size: 12px;
          line-height: 30px;
          text-align: center;
        }

        // 日期内容显示
        .c7n-pro-calendar-date {
          color: var(--datepicker-text);
          background: rgba(26, 58, 139, 0.6);
          border: 1px solid rgba(185, 212, 255, 0.5);
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.2s ease;
          width: 30px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          font-size: 12px;
          margin: 1px;
        }

        // 选中状态
        &.c7n-pro-calendar-selected .c7n-pro-calendar-date {
          background: var(--datepicker-selected);
          color: var(--datepicker-text);
          font-weight: bold;
          border-color: var(--datepicker-selected);
        }

        // 今天日期
        &.c7n-pro-calendar-today:not(.c7n-pro-calendar-selected) .c7n-pro-calendar-date {
          background: var(--datepicker-today);
          font-weight: bold;
          border-color: var(--datepicker-hover);
        }

        // 悬停效果
        &:hover .c7n-pro-calendar-date {
          background-color: rgba(74, 144, 226, 0.3);
          border-color: var(--datepicker-hover);
          transform: scale(1.1);
        }

        // 其他月份日期
        &.c7n-pro-calendar-last-month-cell .c7n-pro-calendar-date,
        &.c7n-pro-calendar-next-month-cell .c7n-pro-calendar-date {
          color: rgba(255, 255, 255, 0.4);
          background: rgba(26, 58, 139, 0.2);
        }
      }
      }
    }
  }

  // 底部按钮区域
  .c7n-pro-calendar-footer {
    display: flex;
    justify-content: flex-end;
    padding: 12px 16px;
    border-top: 1px solid var(--datepicker-border);
    background: transparent;

    button {
      color: #b9d4ff;
      background: rgba(26, 58, 139, 0.3);
      border: 1px solid rgba(185, 212, 255, 0.3);
      border-radius: 4px;
      padding: 6px 16px;
      cursor: pointer;
      margin-left: 8px;
      transition: all 0.2s ease;

      &:hover {
        color: var(--datepicker-text);
        background-color: var(--datepicker-border);
        border-color: var(--datepicker-hover);
      }

      &.c7n-pro-btn-primary {
        background: var(--datepicker-selected);
        border-color: var(--datepicker-selected);
        color: var(--datepicker-text);

        &:hover {
          background: #3a7bd5;
          border-color: #3a7bd5;
        }
      }
    }
  }
}

// 性能优化：使用 transform 和 opacity 进行动画
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup) {
  // 使用 GPU 加速
  will-change: transform, opacity;

  // 确保正确的层级和交互
  pointer-events: auto;

  // 隐藏状态
  &.c7n-pro-popup-hidden {
    opacity: 0;
    transform: scale(0.95);
    pointer-events: none;
    transition: opacity 0.2s ease, transform 0.2s ease;
  }

  // 显示状态
  &:not(.c7n-pro-popup-hidden) {
    opacity: 1;
    transform: scale(1);
    transition: opacity 0.2s ease, transform 0.2s ease;
  }
}

// 确保输入框样式不受影响
:global(.custom-date-picker) {
  .c7n-pro-input {
    background: transparent;
    border: none;
    color: var(--datepicker-text);

    &::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }
  }

  .c7n-pro-input-suffix {
    color: var(--datepicker-text);
  }
}