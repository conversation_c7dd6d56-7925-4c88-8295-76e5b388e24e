/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-07-15 14:17:05
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-08-03 14:28:38
 * @FilePath: \institute-front\packages\key-hmes-front\src\routes\workshop\InspectionMonitorBoard\index.module.less
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
:global(html) {
  /* 1rem = 10px on a 1920px screen */
  font-size: calc(100vw / 192);
}

.boardContainer {
  background-color: #0d1224;
  background-image: url(../../assets/IncomingInspectionDashboard/bac.png);
  background-position: top center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  color: #fff;
  height: 100vh;
  position: relative;
  box-sizing: border-box;
  font-family: 'Microsoft YaHei', sans-serif;


}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  position: absolute;
  height: 6rem;
  width: 100%;
  padding: 0 3rem;
  box-sizing: border-box;
  top: 0;
  left: 0;
}

.headerLeft,
.headerRight {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
}

.headerRight {
  justify-content: flex-end;
  margin-right: 12rem;
}

.datePickerWrapper {
  background-image: url(../../assets/IncomingInspectionDashboard/time_bac.png);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  width: 18rem;
  height: 3rem;
  position: relative;
  overflow: hidden;

  :global {
    .c7n-pro-calendar-picker-wrapper {
      background: transparent !important;
      border: none !important;
      height: 100% !important;
      width: 86% !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      box-shadow: none !important;
      outline: none !important;
    }

    .c7n-pro-calendar-picker {
      background-color: transparent !important;
      border: none !important;
      height: 100% !important;
      width: 100% !important;
      color: #c3e1e9 !important;
      font-size: 1.4rem !important;
      padding: 0 1.5rem 0 4.6rem !important;
      display: flex !important;
      align-items: center !important;

      input {
        background: transparent !important;
        border: none !important;
        color: #b9d4ff !important;
        font-size: 1.4rem !important;
        width: 100% !important;
        text-align: left !important;
        box-shadow: none !important;
        outline: none !important;
        &::placeholder {
          color: #b9d4ff !important;
          opacity: 1;
        }
        &:focus {
          background: transparent !important;
          border: none !important;
          box-shadow: none !important;
          outline: none !important;
        }
      }
    }

    .c7n-pro-calendar-picker-inner {
      background: transparent !important;
      width: 100% !important;
      height: 100% !important;
    }

    .c7n-pro-calendar-picker-suffix .icon {
      color: #b9d4ff !important;
    }

    .icon-date_range {
      color: #c3e1e9 !important;
    }
  }
}

// 全局样式，确保自定义时间选择器正确显示
:global(.custom-date-picker) {
  .c7n-pro-calendar-picker-wrapper {
    background: transparent !important;
    border: none !important;
    height: 100% !important;
    width: 100% !important;
  }

  .c7n-pro-calendar-cell-inner {
    width: 100% !important;
    color: #fff !important;
    height: 100% !important;
    font-size: 12px;
  }

  .c7n-pro-calendar-picker {
    background: transparent !important;
    border: none !important;
    height: 100% !important;
    width: 100% !important;
  }
}

// DatePicker样式已移至datepicker-fix.module.less文件中，避免重复定义

// DatePicker样式已移至datepicker-fix.module.less文件中，避免重复定义

.dateLabel {
  font-size: 1.4rem;
  color: #b9d4ff;
}

.titleGroup {
  flex: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.title {
  background-image: url(../../assets/IncomingInspectionDashboard/header_title.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 30px;
  width: 404px;
}

.enTitle {
  font-size: 1.4rem;
  letter-spacing: 0.2rem;
  color: #fff;
  opacity: 0.7;
  padding-top: 5px;
}

.headerCenter {
  position: absolute;
  top: 70%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 10;
}

.titleGradient {
  background: linear-gradient(#78fdf7 0%, #ffffff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.headerInfo {
  display: flex;
  align-items: center;
  gap: 3rem;
  z-index: 1;
  font-size: 1.6rem;
  width: 100%;
  justify-content: flex-end;
}

.timeWrapper {
  display: flex;
  flex-direction: row;
  align-items: end;
  gap: 1.5rem;
  background-position: left center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 10px 0;
}
.time {
  font-size: 2.2rem;
  color: #fff;
}
.date {
  font-size: 1.6rem;
  color: #fff;
}
.week {
  font-size: 1.6rem;
  color: #fff;
}

.mainContent {
  position: absolute;
  width: 100%;
  height: calc(100% - 9.5rem);
  top: 9.5rem;
  left: 0;
  padding: 0 2rem 2rem;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.topRow {
  display: flex;
  height: 56%;

  > .panel {
    flex: 1;
    min-width: 0;
  }

  > .panel:nth-child(2) {
    margin: 0 3rem 0 6rem;
  }
}

.bottomRow {
  display: grid;
  grid-template-columns: 4.5fr 4.7fr;
  gap: 4rem;
  height: 42%;
}

.panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
  background: transparent;
  padding-top: 0;
}

.panelHeader {
  display: flex;
  align-items: center;
  height: 4.4rem;
  justify-content: space-between;
  margin-bottom: 1rem;
  flex-shrink: 0;
  padding: 1.3rem 0 0 6rem;
  .panelTitle {
    font-size: 1.8rem;
    font-weight: bold;
    color: #fff;
    padding: 1.5rem 0;
    flex-shrink: 0;
    position: relative;
    display: flex;
    align-items: center;
    .title {
      background-image: url(../../assets/IncomingInspectionDashboard/header_title.png);
      background-size: 100% 100%;
      background-position: center;

      height: 40px;
      width: 40px;
    }
  }

  .panelExtra {
    display: flex;
    gap: 1rem;
    margin-top: 0.7rem;
  }
}

.assetButton {
  background-image: url(../../assets/IncomingInspectionDashboard/button_bac.png);
  background-size: 100% 100%;
  background-position: center;
  background-color: transparent;
  border: none;
  color: #00d1ff;
  padding: 0.3rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.4rem;
}

.button {
  background-color: transparent;
  border: 1px solid #00d1ff;
  color: #00d1ff;
  padding: 0.5rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.4rem;
  box-shadow: 0 0 5px #00d1ff;
  &.primary {
    background-color: #00d1ff;
    color: #fff;
    box-shadow: 0 0 5px #00d1ff, inset 0 0 5px rgba(255, 255, 255, 0.5);
  }
}

.panelBody {
  flex-grow: 1;
  padding: 0 1.5rem;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.chartPanelBody {
  padding: 0;
  margin-top: 4.4rem;
  position: relative;
}

.pieChartBackground {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 70%;
  height: 70%;
  transform: translate(-50%, -50%);
  background-image: url(../../assets/IncomingInspectionDashboard/pie_bac.png);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.tableContainer {
  flex-grow: 1;
  overflow: hidden;
  margin-top: 1rem;
}

.customTable {
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #fff;
}

.tableHeader,
.tableRow {
  display: grid;
  grid-template-columns: 1.5fr 1.5fr 1.5fr 1.5fr 1.2fr 1fr;
  text-align: center;
  padding: 1rem;
  align-items: center;
  gap: 2rem;
}
.tableHeader2,
.tableRow2 {
  display: grid;
  grid-template-columns: 0.5fr 1.5fr 1fr 1fr;
  text-align: center;
  padding: 1rem;
  align-items: center;
  gap: 2rem;
}

.tableHeader,
.tableHeader2 {
  background-image: url(../../assets/IncomingInspectionDashboard/table_title.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-color: transparent;
  font-weight: bold;
  color: #c3e1e9;
  font-size: 1.6rem;
  > span {
    white-space: nowrap;
  }
}

.tableBody,
.materialFilterModalBody .materialFilterListBody {
  &::-webkit-scrollbar {
    width: 1.2rem !important;
  }
  &::-webkit-scrollbar-track {
    background-color: #0b1a3e !important;
    border: 2px solid #6274a4 !important;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #869ecd !important;
    border-radius: 0.5rem !important;
    border: 3px solid transparent !important;
    background-clip: content-box !important;
  }
}

.tableBody {
  flex-grow: 1;
  overflow-y: auto;
  color: #b9d4ff;
  font-size: 1.6rem;
  scroll-behavior: smooth;
}

.tableRow,
.tableRow2 {
  border-bottom: 1px solid #16366b;
  transition: all 0.3s ease;
  opacity: 1;
  transform: translateY(0);

  &:nth-child(even) {
    background-color: transparent;
  }

  // 悬停效果
  &:hover {
    background-color: rgba(26, 58, 139, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.1);
  }

  // 行进入动画
  &.entering {
    opacity: 0;
    transform: translateY(20px);
    animation: rowFadeIn 0.5s ease forwards;
  }
}

.clickableLink {
  color: #00ffc5;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.3s ease;

  &:hover {
    color: #00d4a3;
    text-decoration: underline;
  }
}
.activeRow {
  background-color: rgba(0, 132, 255, 0.3) !important;
}
.tableCell {
  white-space: nowrap;
  overflow: hidden;
  padding: 0 0.5rem;
  position: relative;
  max-width: 100%;
  min-width: 0;
}

.scrollingText {
  display: inline-block;
  animation: scrollText var(--scroll-duration, 8s) linear infinite alternate;
  will-change: transform;
  transform: translateX(0);
}

@keyframes scrollText {
  0% {
    transform: translateX(0);
  }
  15% {
    transform: translateX(0);
  }
  85% {
    transform: translateX(var(--scroll-amount, -100px));
  }
  100% {
    transform: translateX(var(--scroll-amount, -100px));
  }
}
.alignRight {
  text-align: right;
  padding-right: 2rem;
}

.loadingRow {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  color: #b9d4ff;
  font-size: 1.2rem;
  opacity: 0.8;
  background: linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.1), transparent);
  animation: loadingShimmer 2s ease-in-out infinite;

  span {
    animation: loadingPulse 1.5s ease-in-out infinite;
    position: relative;

    &::after {
      content: '...';
      animation: loadingDots 1.5s steps(4, end) infinite;
    }
  }
}

// 行淡入动画
@keyframes rowFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// 加载文字脉动动画
@keyframes loadingPulse {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

// 加载背景闪烁动画
@keyframes loadingShimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

// 加载点动画
@keyframes loadingDots {
  0%,
  20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%,
  100% {
    content: '...';
  }
}
.tableIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url(../../assets/IncomingInspectionDashboard/bottom_content_num.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  width: 48px;
  height: 20px;
  margin: 0 8px;
}

:global(.filter-modal .c7n-pro-modal-content) {
  background: transparent !important;
  box-shadow: none !important;
}

.materialFilterModalContainer {
  color: #fff;
  display: flex;
  flex-direction: column;
  height: 52rem;
  width: 750px;
  box-sizing: border-box;
  margin-top: 5rem;
}

.materialFilterModalTitle {
  text-align: center;
  font-size: 24px;
  padding: 15px 0 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;

  .titleText {
    margin: 0 15px;
  }

  .titleDecorator {
    font-size: 20px;
    color: #4a90e2;
  }
}

.materialFilterModalBody {
  flex-grow: 1;
  padding: 0;
  overflow-y: auto;
}

.materialFilterList {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 筛选控件样式
.materialFilterControls {
  padding: 20px 30px;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  flex-shrink: 0;
}

.filterRow {
  display: flex;
  align-items: center;
  gap: 16px; // 添加元素之间的间距
}

.filterItem {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
}

.filterLabel {
  color: #c3e1e9;
  font-size: 14px;
  white-space: nowrap;
  min-width: 80px;
}

.filterInput {
  flex: 1;
  padding: 6px 12px;
  background-color: rgba(11, 26, 62, 0.8);
  border: 1px solid rgba(74, 144, 226, 0.5);
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  min-width: 120px;

  &::placeholder {
    color: rgba(195, 225, 233, 0.6);
  }

  &:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  }
}

.queryButton {
  padding: 6px 16px;
  background-image: url(../../assets/IncomingInspectionDashboard/button_bac.png);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  border: none;
  color: #fff;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 60px;
  flex-shrink: 0;
  margin-right: 10px;
  height: 32px;

  &:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
  }

  &:active {
    transform: scale(0.98);
  }
}

.clearButton {
  padding: 6px 16px;
  background-image: url(../../assets/IncomingInspectionDashboard/button_bac.png);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  border: none;
  color: #fff;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 60px;
  flex-shrink: 0;
  height: 32px;

  &:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
  }

  &:active {
    transform: scale(0.98);
  }
}

.materialFilterListHeader {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 12px 30px;
  background-color: transparent;
  background-image: url(../../assets/IncomingInspectionDashboard/table_title.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  font-weight: bold;
  text-align: left;
  color: #c3e1e9;
  font-size: 16px;
  flex-shrink: 0;
}

.materialFilterListBody {
  flex-grow: 1;
  overflow-y: auto;
}

.materialFilterListRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 15px 30px;
  text-align: left;
  cursor: pointer;
  border-bottom: 1px solid rgba(74, 144, 226, 0.2);
  font-size: 16px;
  color: #b9d4ff;

  &:nth-child(even) {
    background-color: rgba(0, 132, 255, 0.1);
  }

  &:hover {
    background-color: rgba(74, 144, 226, 0.3);
  }

  &.selected {
    background-color: #4a90e2;
    color: #fff;
  }

  &:last-child {
    border-bottom: none;
  }
}

// 无数据提示样式
.noDataRow {
  padding: 40px 30px;
  text-align: center;
  color: rgba(195, 225, 233, 0.6);
  font-size: 14px;
  border-bottom: 1px solid rgba(74, 144, 226, 0.2);
}

.materialFilterModalFooter {
  padding: 20px 0 10px;
  text-align: right;
  flex-shrink: 0;
}

.modalButton {
  background-image: url(../../assets/IncomingInspectionDashboard/button_bac.png);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  border: none;
  color: #fff;
  padding: 8px 20px;
  cursor: pointer;
  margin-left: 10px;
  font-size: 14px;
  height: 36px;
  min-width: 80px;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
  }

  &:active {
    transform: scale(0.98);
  }

  &.primary {
    filter: brightness(1.2);
  }
}

.modalOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  background-image: url(../../assets/IncomingInspectionDashboard/modal.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.materialFilterModalContainer {
  background-color: #1a1a2e;
  
  border: 1px solid #4a90e2;
  border-radius: 8px;
  width: 600px;
  max-height: 500px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.materialFilterModalTitle {
  padding: 20px 30px 10px;
  text-align: center;
  flex-shrink: 0;
}

.titleText {
  color: #c3e1e9;
  font-size: 18px;
  font-weight: bold;
}

.titleDecorator {
  color: #4a90e2;
  font-size: 16px;
  margin: 0 10px;
}

.materialFilterModalBody {
  flex-grow: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

// 加载指示器旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
